name: Build and Push Base Image

on:
  push:
    branches:
      - 'pr*'
    paths:
      - 'package.json'
      - 'package-lock.json'
      - 'yarn.lock'
      - 'Dockerfile-base'

jobs:
  build-and-push:
    runs-on: ubuntu-latest

    steps:
      - name: Lock Pull Request
        run: |
          curl -X POST -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -d '{"state":"pending", "description":"Action running, merge disabled", "context":"Lock PR"}' \
          "https://api.github.com/repos/${{ github.repository }}/statuses/${{ github.sha }}"
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract date
        id: vars
        run: echo "IMAGE_TAG=$(date +'%Y%m%d_%H%M%S')" >> $GITHUB_ENV

      - name: Extract repository name
        id: repo
        run: echo "REPO=$(basename ${{ github.repository }})" >> $GITHUB_ENV

      - name: Build and push multi-arch image
        uses: docker/build-push-action@v6
        with:
          platforms: linux/amd64,linux/arm64
          push: true
          file: Dockerfile-base
          tags: jumpserver/${{ env.REPO }}-base:${{ env.IMAGE_TAG }}

      - name: Update Dockerfile
        run: |
          sed -i 's|-base:.* AS stage-build|-base:${{ env.IMAGE_TAG }} AS stage-build|' Dockerfile

      - name: Commit changes
        run: |
          git config --global user.name 'github-actions[bot]'
          git config --global user.email 'github-actions[bot]@users.noreply.github.com'
          git add Dockerfile
          git commit -m "perf: Update Dockerfile with new base image tag"
          git push
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Unlock Pull Request
        run: |
          curl -X POST -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
          -d '{"state":"success", "description":"Action running, merge disabled", "context":"Lock PR"}' \
          "https://api.github.com/repos/${{ github.repository }}/statuses/${{ github.sha }}"
