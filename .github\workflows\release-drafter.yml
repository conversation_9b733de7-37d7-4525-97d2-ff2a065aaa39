on:
  push:
    # Sequence of patterns matched against refs/tags
    tags:
      - 'v*' # Push events to matching v*, i.e. v1.0, v20.15.10

name: Create Release And Upload assets

jobs:
  create-realese:
    name: Create Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v2
      - name: Get version
        id: get_version
        run: |
          TAG=$(basename ${GITHUB_REF})
          echo "::set-output name=TAG::$TAG"
      - name: Create Release
        id: create_release
        uses: release-drafter/release-drafter@v5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          config-name: release-config.yml
          version: ${{ steps.get_version.outputs.TAG }}
          tag: ${{ steps.get_version.outputs.TAG }}
      - uses: actions/setup-node@v2
        with:
          node-version: '20.15'
      - name: Install dependencies
        run: yarn install
      - name: Build web
        run: |
          sed -i "s@version-dev@${{ steps.get_version.outputs.TAG }}@g" src/layout/components/NavHeader/About.vue
          yarn build
      - name: Create Upload Assets
        run: |
          rm -rf build/*
          mv lina lina-${{ steps.get_version.outputs.TAG }}
          tar -czf lina-${{ steps.get_version.outputs.TAG }}.tar.gz lina-${{ steps.get_version.outputs.TAG }}
          echo $(md5sum lina-${{ steps.get_version.outputs.TAG }}.tar.gz | awk '{print $1}') > build/lina-${{ steps.get_version.outputs.TAG }}.tar.gz.md5
          mv lina-${{ steps.get_version.outputs.TAG }}.tar.gz build/
      - name: Release Upload Assets
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          draft: true
          files: |
            build/lina-${{ steps.get_version.outputs.TAG }}.tar.gz
            build/lina-${{ steps.get_version.outputs.TAG }}.tar.gz.md5
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
