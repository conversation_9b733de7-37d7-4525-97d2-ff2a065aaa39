{"name": "lina", "version": "v4.0.0", "description": "JumpServer Web UI", "author": "JumpServer Team <<EMAIL>>", "license": "GPL-3.0-or-later", "scripts": {"dev": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service serve", "serve": "vue-cli-service serve", "build": "NODE_OPTIONS=--openssl-legacy-provider vue-cli-service build", "build:prod": "vue-cli-service build", "build:stage": "vue-cli-service build --mode staging", "preview": "node build/index.js --preview", "lint": "eslint --ext .js,.vue src", "fix": "eslint --ext .js,.vue --fix src", "test:unit": "jest --clearCache && vue-cli-service test:unit", "test:ci": "npm run lint && npm run test:unit", "svgo": "svgo -f src/icons/svg --config=src/icas/svgo.yml", "vue-i18n-extract": "vue-i18n-extract", "vue-i18n-report": "vue-i18n-extract report -v './src/**/*.?(js|vue)' -l './src/i18n/langs/**/*.json'", "vue-i18n-report-json": "vue-i18n-extract report -v './src/**/*.?(js|vue)' -l './src/i18n/langs/**/*.json' -o /tmp/abc.json", "vue-i18n-report-add-miss": "vue-i18n-extract report -v './src/**/*.?(js|vue)' -l './src/i18n/langs/**/*.json' -a", "diff-i18n": "python ./src/i18n/langs/i18n-util.py diff en ja zh_Hant", "apply-i18n": "python ./src/i18n/langs/i18n-util.py apply en ja zh_Hant"}, "dependencies": {"@babel/plugin-proposal-optional-chaining": "^7.13.12", "@fontsource/open-sans": "^5.0.24", "@traptitech/markdown-it-katex": "^3.6.0", "@ztree/ztree_v3": "3.5.44", "axios": "0.28.0", "axios-retry": "^3.1.9", "caniuse-lite": "^1.0.30001642", "cron-parser": "^4.0.0", "crypto-js": "^4.1.1", "css-color-function": "^1.3.3", "decimal.js": "^10.4.3", "deepmerge": "^4.2.2", "dompurify": "^3.1.6", "echarts": "4.7.0", "element-ui": "2.15.14", "eslint-plugin-html": "^6.0.0", "highlight.js": "^11.9.0", "install": "^0.13.0", "jquery": "^3.6.1", "js-cookie": "2.2.0", "jsencrypt": "^3.2.1", "less": "^3.10.3", "less-loader": "^5.0.0", "lodash": "^4.17.21", "lodash.clonedeep": "^4.5.0", "lodash.frompairs": "^4.0.1", "lodash.get": "^4.4.2", "lodash.has": "^4.5.2", "lodash.includes": "^4.3.0", "lodash.isempty": "^4.4.0", "lodash.isequal": "^4.5.0", "lodash.isplainobject": "^4.0.6", "lodash.set": "^4.3.2", "lodash.topairs": "^4.3.0", "lodash.values": "^4.3.0", "markdown-it": "^13.0.2", "markdown-it-link-attributes": "^4.0.1", "moment": "^2.29.4", "moment-parseformat": "^4.0.0", "normalize.css": "7.0.0", "npm": "^7.8.0", "nprogress": "0.2.0", "path-to-regexp": "3.3.0", "v-sanitize": "^0.0.13", "vue": "2.6.10", "vue-codemirror": "4.0.6", "vue-cookie": "^1.1.4", "vue-echarts": "^5.0.0-beta.0", "vue-i18n": "^8.15.5", "vue-json-editor": "^1.4.3", "vue-markdown": "^2.2.4", "vue-moment": "^4.1.0", "vue-password-strength-meter": "^1.7.2", "vue-router": "3.0.6", "vue-select": "^3.9.5", "vuejs-logger": "^1.5.4", "vuex": "3.1.0", "watermark-js-plus": "^1.5.8", "xss": "^1.0.14", "xterm": "^4.5.0", "xterm-addon-fit": "^0.3.0", "zxcvbn": "^4.4.2"}, "devDependencies": {"@babel/core": "7.18.6", "@babel/register": "7.0.0", "@vue/cli-plugin-babel": "3.6.0", "@vue/cli-plugin-eslint": "^3.9.1", "@vue/cli-plugin-unit-jest": "3.6.3", "@vue/cli-service": "3.6.0", "@vue/test-utils": "1.0.0-beta.29", "autoprefixer": "^9.5.1", "babel-core": "7.0.0-bridge.0", "babel-eslint": "10.0.1", "babel-jest": "23.6.0", "chalk": "2.4.2", "compression-webpack-plugin": "^6.1.1", "connect": "3.6.6", "deasync": "^0.1.29", "element-theme-chalk": "^2.13.1", "eslint": "^5.15.3", "eslint-plugin-vue": "5.2.2", "eslint-plugin-vue-i18n": "^0.3.0", "github-markdown-css": "^5.1.0", "html-webpack-plugin": "3.2.0", "husky": "^4.2.3", "less-loader": "^5.0.0", "lint-staged": "^10.1.2", "mockjs": "1.0.1-beta3", "runjs": "^4.3.2", "sass": "~1.32.6", "sass-loader": "^7.1.0", "script-ext-html-webpack-plugin": "2.1.3", "script-loader": "0.7.2", "serve-static": "^1.16.0", "strip-ansi": "^7.1.0", "svg-sprite-loader": "4.1.3", "svgo": "1.2.2", "vue-i18n-extract": "^1.1.1", "vue-template-compiler": "2.6.10"}, "engines": {"node": ">=8.9", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 4 versions", "ie 11"], "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix"]}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}