name: "Run Build Test"
on:
  push:
    paths:
      - 'Dockerfile'
      - 'Dockerfile*'
      - 'package.json'
      - 'yarn.lock'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        component: [ lina ]
        version: [ v4 ]
    steps:
      - uses: actions/checkout@v4
      - uses: docker/setup-buildx-action@v3

      - name: Prepare Build
        run: |
          sed -i '<EMAIL>@registry.yarnpkg.com@g' yarn.lock

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build Image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: true
          file: Dockerfile
          tags: ghcr.io/jumpserver/${{ matrix.component }}:${{ matrix.version }}
          platforms: linux/amd64
          build-args: |
            VERSION=${{ matrix.version }}
            APT_MIRROR=http://deb.debian.org
            NPM_REGISTRY=https://registry.yarnpkg.com
          outputs: type=image,oci-mediatypes=true,compression=zstd,compression-level=3,force-compression=true
          cache-from: type=gha
          cache-to: type=gha,mode=max
