/* 这里只写element-variables里面改不动的、且和主题系列颜色相关的样式！！！ */
/* 主题相关看该文件夹下的README.md */

/* info type special treatment */
.el-button--info {
  background-color: info;
  border-color: info;
}

.el-button--text {
  padding: 5px
}

.el-button--text:hover {
  background-color: rgba(0, 0, 0, .05);
}

.el-button--success {
  background-color: success;
  border-color: success;
}

.el-alert--info.is-light {
  background-color: rgba(255, 255, 255, 0.5);
  color: info;
  border: 1px solid;
}

.el-alert--info .el-alert__description {
  color: info;
}

.el-pagination.is-background {
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pager {
        color: var(--color-icon-primary);
    }
}

.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #fff;
  background-color: primary;
}

.el-pagination.is-background .btn-next,
.el-pagination.is-background .btn-prev,
.el-pagination.is-background .el-pager li {
    margin: 0 5px;
    background-color: #fff;
    color: var(--color-icon-primary);
    min-width: 28px;
    border-radius: 2px;
    border: 1px solid #DCDFE6;
    font-size: 12px;
    font-weight: 400;
}

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}

.el-upload input[type="file"] {
  display: none !important;
}

.el-upload__input {
  display: none;
}

.upload-container .el-upload {
    width: 100%;
}

.upload-container .el-upload .el-upload-dragger {
  width: 100%;
  height: 200px;
}

.el-dropdown-menu a {
  display: block
}

.el-table th > .cell {
  color: rgb(104, 106, 108);
}

.el-range-separator {
  box-sizing: content-box;
}


td .el-button.el-button--mini {
  padding: 1px 6px;
  line-height: 1.5;

  .el-icon--right {
      margin-bottom: 2px;
  }
}

.el-tabs__item.is-active, .el-tabs--border-card>.el-tabs__header .el-tabs__item.is-active {
  color: #555555;
}

.el-tabs--border-card>.el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #555555;
}
.main-container {
  background-color: #f3f3f4;
}

.el-dropdown:hover {
  cursor: pointer;
}

.el-dropdown-menu__item:focus, .el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: #f5f5f5;
  color: inherit;
}

.el-tabs__item:hover {
  color: inherit;
}

.el-tabs__item.is-active {
  font-weight: 600;
}

.el-button.el-button--default:hover:not(.is-disabled) {
  color: #606266;
  border-color: #d2d2d2;
  background-color: #e6e6e6;
}

.el-button-group>.el-dropdown>.el-button {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  border-left-color: rgb(220, 223, 230);
}

.el-alert__content {
  line-height: 1.5;
}

.el-card__header {
  padding: 10px 15px;
  font-size: 14px;
  line-height: 18.5px;
  font-weight: normal;
  color: #333;
}

.el-card.primary > .el-card__header {
  background-color: primary;
  border-color: primary;
  color: white;
}

.el-card.success > .el-card__header {
  background-color: success;
  border-color: success;
  color: #ffffff;
}

.el-card.info > .el-card__header {
  background-color: info;
  border-color: info;
  color: #ffffff;
}

.el-card.warning > .el-card__header {
  background-color: warning;
  border-color: warning;
  color: #ffffff;
}

.el-card.danger > .el-card__header {
  background-color: danger;
  border-color: danger;
  color: #ffffff;
}

.el-input__inner {
  background-color: #FFFFFF;
  background-image: none;
  border: 1px solid #e5e6e7;
  border-radius: 1px;
  color: inherit;
  display: block;
  padding: 6px 12px;
  transition: border-color 0.15s ease-in-out 0s, box-shadow 0.15s ease-in-out 0s;
  width: 100%;
  font-size: 14px;
  line-height: 1.5;
  height: 34px;
}

.el-input--small .el-input__inner {
  height: 34px;
}

.el-input--small .el-input__icon {
  line-height: 30px;
}

.option-group .el-select-dropdown__item.hover, .option-group .el-select-dropdown__item.selected {
  background-color: primary;
  color: white;
}

.option-group:has(.hover) .el-select-dropdown__item.selected {
  background-color: light-2;
}


.el-select-dropdown__item.is-disabled:hover{
  color:#c0c4cc;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected::after {
  color: primary;
}

.el-select-dropdown.is-multiple .el-select-dropdown__item.selected.hover {
  color: white;
  background-color: light-4;
}

.el-tag.el-tag--info {
  background-color: #f1f1f1!important;
  border-color: #e5e6e7!important;
  color: #333333!important;
}

.el-tag.el-tag--info .el-tag__close {
    display: inline-block;
    margin-top: 3px;
    color: var(--color-text-primary);
    background-color: inherit;
}

.el-tag.el-tag--info.is-hit {
  border-color: #e5e6e7!important;
}

.el-tag.el-tag--info .el-tag__close:hover {
  color: #000000;
  font-weight: 600;
  background-color: inherit;
}

.el-table .ascending .sort-caret.ascending {
  border-bottom-color: #676a6c;
}

.el-table .descending .sort-caret.descending {
  border-top-color: #676a6c;
}

.text-link {
  color: info!important;
}

.text-link:hover {
  color: info!important;
  filter: opacity(65%)!important;
}

.text-danger {
  color: danger;
}

.text-primary {
  color: primary;
}

.text-info {
  color: info;
}

.text-warning {
  color: warning;
}

.text-success {
  color: success;
}

.el-radio__input.is-checked+.el-radio__label {
  color: inherit;
}

.el-textarea__inner {
    color: var(--color-text-primary);
}

.el-pagination.is-background .number {
  padding: 0;
}

.el-card.primary .el-card__header {
  background-color: primary;
}

.el-card.success .el-card__header {
  background-color: success;
}

.el-card.info .el-card__header {
  background-color: info;
}

.el-card.warning .el-card__header {
  background-color: warning;
}

.el-card.danger .el-card__header {
  background-color: danger;
}

.el-message-box__headerbtn .el-message-box__close {
  color: #606266;
}

.el-tooltip__popper.is-light {
  background: #FFF;
  max-width: 500px;
  border: 1px solid #e7eaec;
  box-shadow: 0 1.6px 3.6px 0 rgba(0, 0, 0, .132), 0 .3px .9px 0 rgba(0, 0, 0, .108);
  line-height: 1.5;
  padding: 10px;
}

.el-dialog__headerbtn .el-dialog__close {
  color: #000;
  opacity: .2;
}

.el-table__header thead tr th {
  /*border-bottom: 1px solid #e7e7e7 !important;*/
}

.el-table .cell,
.el-table td:first-child .cell,
.el-table th:first-child .cell {
  padding-left: 10px;
  padding-right: 14px;
}

.el-tag--default.el-tag--dark {
  background-color: #d1dade;
  color: #5e5e5e;
  border: none;
}

.el-card {
  color: #676a6c;
}

.el-table__empty-block {
 width: 100% !important;
}

.el-dialog__headerbtn .el-dialog__close {
  font-size: 21px;
  font-weight: 700;
  color: #000;
  text-shadow: 0 1px 0 #fff;
}

.el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
  color: #000;
  font-size: 22px;
  font-weight: 800;
}

.el-tag--default.el-tag--dark {
  background-color: #d1dade;
  color: #5e5e5e;
  border: none;
}

.el-card {
  color: #676a6c;
}

.el-table__empty-block {
 width: 100% !important;
}

.el-dialog__headerbtn .el-dialog__close {
  font-size: 21px;
  font-weight: 700;
  color: #000;
  text-shadow: 0 1px 0 #fff;
}

.el-dialog__headerbtn:focus .el-dialog__close, .el-dialog__headerbtn:hover .el-dialog__close {
  color: #000;
  font-size: 22px;
  font-weight: 800;
}

.el-button--danger.is-plain {
  color: danger;
  background: #ffffff;
  border-color: danger;
}

.el-button--danger.is-plain.is-disabled,
.el-button--danger.is-plain.is-disabled:active,
.el-button--danger.is-plain.is-disabled:focus,
.el-button--danger.is-plain.is-disabled:hover {
  color: white;
}

.el-alert .el-alert__description {
  margin: 1px 0 0;
}

.el-table {
  font-size: 13px;
}

.el-dialog {
  position: absolute;
  top: 50%;
  left: 50%;
  margin: 0 !important;
  transform: translate(-50%, -50%);
  max-height: calc(100% - 30px);
  max-width: calc(100% - 30px);
  display: flex;
  flex-direction: column;
}

.el-dialog .el-dialog__header .el-dialog__title {
    color: var(--color-text-primary);
}

.el-dialog  .el-dialog__body {
  max-height: 80vh;
  overflow: auto;
  padding: 30px;
}

.el-dialog .el-dialog__body .el-transfer-panel .el-transfer-panel__body .el-input__inner,
.el-dialog .el-dialog__body .el-transfer-panel .el-transfer-panel__header .el-checkbox__label,
.el-dialog .el-dialog__body .el-transfer-panel .el-transfer-panel__body .el-checkbox-group .el-checkbox.el-transfer-panel__item {
    color: var(--color-text-primary);
}

.el-dialog .el-dialog__body .opera .el-button.is-disabled,
.el-dialog .el-dialog__body .el-transfer-panel .vip-footer .el-button.is-disabled {
    color: var(--color-input-border);
}

.el-dialog .el-dialog__body .opera .el-button.is-disabled.el-button--primary {
    color: #fff;
}

.el-dialog .el-dialog__body form {
  padding-right: 20px;
  margin-right: 20px;
}

.el-dialog .el-dialog__footer {
  padding: 10px 30px 30px;
}

.el-cascader {
  line-height: 34px;
}

.el-input__icon {
  line-height: 34px;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #606266;
}

.el-select-dropdown__item.selected {
  font-weight: 400;
  color: white;
  background-color: primary;
}

.el-input-group__prepend div.el-select .el-input__inner,
.el-input-group__prepend div.el-select .el-input__inner:hover {
  color: #303133;
}

.el-input-group__append, .el-input-group__prepend {
  color: primary
}

.el-input.is-disabled .el-input__inner {
  color: $--color-text-primary;
  cursor: not-allowed;
}

.el-step__description.is-finish {
  color: #676a6c;
}

.el-alert {
  border: solid 1px #e7eaec;
}

.el-alert.el-alert--success.is-light {
  border-color: var(--color-success-light);
}

.el-alert.el-alert--primary.is-light {
  border-color: var(--color-primary-light);
}

.el-alert.el-alert--info.is-light {
  border-color: var(--color-info-light);
}

.el-alert.el-alert--warning.is-light {
  border-color: var(--color-warning-light);
}

.el-alert.el-alert--error.is-light {
  border-color: var(--color-danger-light);
}

#nprogress .bar {
  background: light-5!important;
}

#nprogress .peg {
  box-shadow: 0 0 10px light-5, 0 0 5px light-5!important;
}
